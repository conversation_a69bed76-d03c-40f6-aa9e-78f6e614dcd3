# version: '3.8'

services:
  app:
    image: ${DOCKER_USERNAME:-brey<PERSON>}/${IMAGE_NAME:-cbbc-app}:${VERSION:-latest}
    # Uncomment the build section below for local builds
    # build:
    #   context: .
    #   dockerfile: Dockerfile
    container_name: cbbc-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8000}:8000"
    environment:
      - FLASK_ENV=production
      - PYTHONUNBUFFERED=1
      - platform=sqlite
      - DATABASE_URL=sqlite:///instance/app.db
      - OUT_PATH=/app/data
      - TZ=Asia/Hong_Kong
      - SELENIUM_HUB_URL=http://selenium:4444/wd/hub
    volumes:
      # Persistent data storage
      - app_data:/app/data
      - app_instance:/app/instance
      - app_logs:/app/logs
      # Optional: Mount external data directory
      # - ./data:/app/data
    networks:
      - cbbc_network
    depends_on:
      - selenium
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/market-status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  selenium:
    image: selenium/standalone-chrome:latest
    container_name: cbbc-selenium
    restart: unless-stopped
    ports:
      - "${SELENIUM_PORT:-4444}:4444"
      - "${VNC_PORT:-7900}:7900"
    environment:
      - SE_VNC_NO_PASSWORD=1
      - SE_NODE_MAX_SESSIONS=2
      - SE_NODE_SESSION_TIMEOUT=300
      - TZ=Asia/Hong_Kong
    volumes:
      - selenium_downloads:/home/<USER>/Downloads
    networks:
      - cbbc_network
    shm_size: 2gb
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4444/wd/hub/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: cbbc-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      # - ./ssl:/etc/nginx/ssl:ro  # Uncomment for SSL certificates
    networks:
      - cbbc_network
    depends_on:
      - app
    profiles:
      - with-nginx

volumes:
  app_data:
    driver: local
  app_instance:
    driver: local
  app_logs:
    driver: local
  selenium_downloads:
    driver: local

networks:
  cbbc_network:
    driver: bridge
