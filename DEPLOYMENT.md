# CBBC Application - Production Deployment Guide

This guide provides comprehensive instructions for deploying the CBBC application as a private Docker image on DockerHub.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start](#quick-start)
3. [Building the Docker Image](#building-the-docker-image)
4. [Pushing to DockerHub](#pushing-to-dockerhub)
5. [Deployment Options](#deployment-options)
6. [Configuration](#configuration)
7. [Monitoring and Maintenance](#monitoring-and-maintenance)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Software
- Docker (version 20.10 or later)
- Docker Compose (version 2.0 or later)
- DockerHub account with private repository access

### System Requirements
- **Minimum**: 2 CPU cores, 4GB RAM, 10GB disk space
- **Recommended**: 4 CPU cores, 8GB RAM, 20GB disk space
- **Operating System**: Linux, macOS, or Windows with WSL2

## Quick Start

1. **Clone and prepare the repository**:
   ```bash
   git clone <your-repo-url>
   cd cbbc-app
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Build and deploy**:
   ```bash
   # Set your DockerHub username
   export DOCKER_USERNAME=your-dockerhub-username
   
   # Build the image
   ./scripts/build.sh
   
   # Push to DockerHub
   ./scripts/push.sh
   
   # Deploy the application
   ./scripts/deploy.sh
   ```

4. **Access the application**:
   - Open http://localhost:8000 in your browser
   - API endpoints available at http://localhost:8000/api/

## Quick Docker Run (Alternative)

For a quick test without Docker Compose:

```bash
# Run with default SQLite database
docker run --rm -p 8000:8000 \
  -v cbbc_data:/app/data \
  -v cbbc_instance:/app/instance \
  --env platform=sqlite \
  --env DATABASE_URL=sqlite:///instance/app.db \
  breyton/cbbc-app:latest

# Run with custom environment file
docker run --rm -p 8000:8000 \
  -v cbbc_data:/app/data \
  -v cbbc_instance:/app/instance \
  --env-file .env.prod \
  breyton/cbbc-app:latest
```

## Building the Docker Image

### Using the Build Script

The easiest way to build the image is using the provided script:

```bash
# Basic build
./scripts/build.sh

# Build with custom parameters
./scripts/build.sh -u your-username -n cbbc-app -v v1.0.0
```

### Manual Build

If you prefer to build manually:

```bash
# Build the production image
docker build -t your-username/cbbc-app:latest -f Dockerfile .

# Tag with version
docker tag your-username/cbbc-app:latest your-username/cbbc-app:v1.0.0
```

### Build Options

The Dockerfile supports multi-stage builds for optimized production images:

- **Build stage**: Installs dependencies and builds the application
- **Production stage**: Creates a minimal runtime image with only necessary components

## Pushing to DockerHub

### Setting Up Private Repository

1. **Create a private repository on DockerHub**:
   - Go to https://hub.docker.com/repository/create
   - Set repository name: `cbbc-app`
   - Set visibility to **Private**
   - Click "Create"

2. **Login to DockerHub**:
   ```bash
   docker login
   ```

### Using the Push Script

```bash
# Push with default settings
./scripts/push.sh

# Push with custom parameters
./scripts/push.sh -u your-username -v v1.0.0
```

### Manual Push

```bash
# Push specific version
docker push your-username/cbbc-app:v1.0.0

# Push latest
docker push your-username/cbbc-app:latest
```

## Deployment Options

### Option 1: Docker Compose (Recommended)

This is the recommended deployment method for production:

```bash
# Deploy with default configuration
./scripts/deploy.sh

# Deploy with custom settings
./scripts/deploy.sh -u your-username -v v1.0.0

# Deploy with nginx reverse proxy
docker-compose -f docker-compose.prod.yml --profile with-nginx up -d
```

### Option 2: Standalone Docker

For simple deployments without additional services:

```bash
# Run the application container
docker run -d \
  --name cbbc-app \
  -p 8000:8000 \
  -v cbbc_data:/app/data \
  -v cbbc_instance:/app/instance \
  --env-file .env.prod \
  your-username/cbbc-app:latest

# Run selenium container (required for web scraping)
docker run -d \
  --name cbbc-selenium \
  -p 4444:4444 \
  -p 7900:7900 \
  --shm-size=2g \
  selenium/standalone-chrome:latest
```

### Option 3: Kubernetes

For enterprise deployments, Kubernetes manifests can be created based on the Docker Compose configuration.

## Configuration

### Environment Variables

Key environment variables for production:

```bash
# Application
FLASK_ENV=production
DATABASE_PLATFORM=sqlite  # or postgresql
OUT_PATH=/app/data

# Security (set these in production!)
SECRET_KEY=your-secret-key-here

# Database (if using PostgreSQL)
POSTGRES_USER=cbbc_user
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=cbbc_db
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Performance
GUNICORN_WORKERS=1
GUNICORN_TIMEOUT=120
```

### Database Options

#### SQLite (Default)
- **Pros**: Simple setup, no additional services required
- **Cons**: Limited concurrent access, not suitable for high-traffic deployments
- **Use case**: Single-user deployments, development, testing

#### PostgreSQL
- **Pros**: Better performance, concurrent access, production-ready
- **Cons**: Requires additional database service
- **Use case**: Production deployments, multiple users

To use PostgreSQL:
1. Set `DATABASE_PLATFORM=postgresql`
2. Configure PostgreSQL connection parameters
3. Add PostgreSQL service to docker-compose.yml

### Volume Mounts

Important directories to persist:

- `/app/data`: Application data and logs
- `/app/instance`: SQLite database (if using SQLite)
- `/home/<USER>/Downloads`: Selenium downloads (in selenium container)

## Monitoring and Maintenance

### Health Checks

The application includes built-in health checks:

```bash
# Check application health
curl http://localhost:8000/api/market-status

# Check container health
docker ps  # Look for "healthy" status
```

### Viewing Logs

```bash
# View application logs
./scripts/deploy.sh logs app 100

# View all service logs
docker-compose -f docker-compose.prod.yml logs -f

# View specific service logs
docker-compose -f docker-compose.prod.yml logs -f selenium
```

### Updating the Application

```bash
# Build new version
./scripts/build.sh -v v1.1.0

# Push to DockerHub
./scripts/push.sh -v v1.1.0

# Deploy new version
./scripts/deploy.sh -v v1.1.0
```

### Backup and Restore

```bash
# Backup data
docker run --rm -v cbbc_data:/data -v $(pwd):/backup alpine tar czf /backup/cbbc-data-backup.tar.gz -C /data .

# Restore data
docker run --rm -v cbbc_data:/data -v $(pwd):/backup alpine tar xzf /backup/cbbc-data-backup.tar.gz -C /data
```

## Troubleshooting

### Common Issues

1. **Container fails to start**:
   ```bash
   # Check logs
   docker logs cbbc-app
   
   # Check configuration
   docker exec cbbc-app env | grep -E "(FLASK|DATABASE|OUT_PATH)"
   ```

2. **Database connection issues**:
   ```bash
   # For SQLite, check file permissions
   docker exec cbbc-app ls -la /app/instance/
   
   # For PostgreSQL, check connection
   docker exec cbbc-app python -c "from config import get_config; print(get_config().get_database_url())"
   ```

3. **Selenium connection issues**:
   ```bash
   # Check selenium container
   docker logs cbbc-selenium
   
   # Test selenium connection
   curl http://localhost:4444/wd/hub/status
   ```

4. **Permission issues**:
   ```bash
   # Check file ownership
   docker exec cbbc-app ls -la /app/
   
   # Fix permissions if needed
   docker exec --user root cbbc-app chown -R appuser:appuser /app/data
   ```

### Performance Tuning

1. **Increase worker processes** (if you have multiple CPU cores):
   ```bash
   # Set in environment
   GUNICORN_WORKERS=2
   ```

2. **Adjust memory limits**:
   ```yaml
   # In docker-compose.yml
   services:
     app:
       deploy:
         resources:
           limits:
             memory: 1G
           reservations:
             memory: 512M
   ```

3. **Enable nginx caching** (if using nginx profile):
   - Uncomment caching directives in nginx.conf
   - Add cache volume in docker-compose.yml

### Getting Help

1. **Check application logs** for error messages
2. **Verify configuration** using environment variables
3. **Test individual components** (database, selenium) separately
4. **Review Docker container status** and resource usage

For additional support, check the application logs and container status before reporting issues.

## Quick Reference

### Essential Commands

```bash
# Build and deploy (complete workflow)
export DOCKER_USERNAME=your-dockerhub-username
./scripts/build.sh && ./scripts/push.sh && ./scripts/deploy.sh

# Stop application
./scripts/deploy.sh stop

# View logs
./scripts/deploy.sh logs app 50

# Update to new version
./scripts/build.sh -v v1.1.0 && ./scripts/push.sh -v v1.1.0 && ./scripts/deploy.sh -v v1.1.0

# Backup data
docker run --rm -v cbbc_data:/data -v $(pwd):/backup alpine tar czf /backup/backup-$(date +%Y%m%d).tar.gz -C /data .
```

### File Structure

```
.
├── Dockerfile                 # Production Docker image
├── docker-compose.prod.yml    # Production deployment
├── .dockerignore             # Files to exclude from build
├── .env.example              # Environment template
├── .env.prod                 # Production environment
├── config.py                 # Application configuration
├── gunicorn.conf.py          # Gunicorn settings
├── nginx.conf                # Nginx configuration
├── scripts/
│   ├── build.sh              # Build Docker image
│   ├── push.sh               # Push to DockerHub
│   └── deploy.sh             # Deploy application
└── DEPLOYMENT.md             # This documentation
```

### Port Mapping

- **8000**: Main application (Dash/Flask)
- **4444**: Selenium WebDriver
- **7900**: Selenium VNC (for debugging)
- **80**: Nginx HTTP (if using nginx profile)
- **443**: Nginx HTTPS (if configured)

### Default Credentials

- **Application**: No authentication by default
- **Selenium VNC**: No password (SE_VNC_NO_PASSWORD=1)

### Important URLs

- **Application**: http://localhost:8000
- **Health Check**: http://localhost:8000/api/market-status
- **Selenium Grid**: http://localhost:4444
- **Selenium VNC**: http://localhost:7900 (for debugging)
