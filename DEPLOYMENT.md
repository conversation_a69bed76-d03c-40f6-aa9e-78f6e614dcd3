# CBBC Application - Production Deployment Guide

This guide provides comprehensive instructions for deploying the CBBC application stack, which includes the main application and a Selenium service for web scraping functionality.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start](#quick-start)
3. [Building the Docker Image](#building-the-docker-image)
4. [Pushing to DockerHub](#pushing-to-dockerhub)
5. [Deployment Options](#deployment-options)
6. [Configuration](#configuration)
7. [Monitoring and Maintenance](#monitoring-and-maintenance)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Software
- Docker (version 20.10 or later)
- Docker Compose (version 2.0 or later)
- DockerHub account with private repository access

### System Requirements
- **Minimum**: 2 CPU cores, 4GB RAM, 10GB disk space
- **Recommended**: 4 CPU cores, 8GB RAM, 20GB disk space
- **Operating System**: Linux, macOS, or Windows with WSL2

### Application Architecture
The CBBC application consists of two main services:
- **cbbc-app**: Main application container (Dash/Flask web application)
- **cbbc-selenium**: Selenium Chrome container for web scraping AASTOCKS data
- **Shared volumes**: For data exchange between containers

## Quick Start

1. **Clone and prepare the repository**:
   ```bash
   git clone <your-repo-url>
   cd cbbc-app
   ```

2. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Build and deploy the application stack**:
   ```bash
   # Set your DockerHub username
   export DOCKER_USERNAME=your-dockerhub-username

   # Build the application image
   ./scripts/build.sh

   # Push to DockerHub
   ./scripts/push.sh
   
   # Deploy the full application stack
   ./scripts/deploy.sh
   ```

4. **Access the application**:
   - **Main Application**: http://localhost:8000
   - **API Endpoints**: http://localhost:8000/api/
   - **Selenium Grid**: http://localhost:4444 (for monitoring)
   - **Selenium VNC**: http://localhost:7900 (for debugging web scraping)

## Quick Docker Run (Alternative - Limited Functionality)

⚠️ **Warning**: Running the application container alone will NOT include web scraping functionality.
For full functionality, use Docker Compose as shown above.

For testing the web interface only:

```bash
# Run with default SQLite database (no web scraping)
docker run --rm -p 8000:8000 \
  -v cbbc_data:/app/data \
  -v cbbc_instance:/app/instance \
  --env platform=sqlite \
  --env DATABASE_URL=sqlite:///instance/app.db \
  breyton/cbbc-app:latest

# Run with custom environment file (no web scraping)
docker run --rm -p 8000:8000 \
  -v cbbc_data:/app/data \
  -v cbbc_instance:/app/instance \
  --env-file .env.prod \
  breyton/cbbc-app:latest
```

## Service Architecture

The CBBC application uses a multi-container architecture for optimal functionality:

### Services Overview

| Service | Container | Purpose | Ports |
|---------|-----------|---------|-------|
| **cbbc-app** | `breyton/cbbc-app:latest` | Main web application (Dash/Flask) | 8000 |
| **cbbc-selenium** | `selenium/standalone-chrome:latest` | Web scraping service | 4444, 7900 |

### Service Details

#### cbbc-app (Main Application)
- **Technology**: Python, Dash, Flask, Gunicorn
- **Functionality**:
  - Web dashboard for CBBC data visualization
  - REST API endpoints
  - Data processing and database operations
  - Background task scheduling
- **Dependencies**: Requires Selenium service for data downloads
- **Health Check**: `GET /api/market-status`

#### cbbc-selenium (Web Scraping Service)
- **Technology**: Selenium Grid with Chrome browser
- **Functionality**:
  - Automated web scraping from AASTOCKS
  - Excel file downloads
  - Headless browser operations
- **VNC Access**: Port 7900 for visual debugging
- **Grid Console**: Port 4444 for monitoring

### Data Flow
1. **User Request** → Main application receives manual update request
2. **Selenium Command** → Application sends WebDriver commands to Selenium service
3. **Web Scraping** → Selenium navigates to AASTOCKS and downloads Excel file
4. **File Processing** → Application processes downloaded Excel file
5. **Database Update** → Processed data is stored in SQLite database
6. **Response** → User receives confirmation and updated dashboard

## Building the Docker Image

### Using the Build Script

The easiest way to build the image is using the provided script:

```bash
# Basic build
./scripts/build.sh

# Build with custom parameters
./scripts/build.sh -u your-username -n cbbc-app -v v1.0.0
```

### Manual Build

If you prefer to build manually:

```bash
# Build the production image
docker build -t your-username/cbbc-app:latest -f Dockerfile .

# Tag with version
docker tag your-username/cbbc-app:latest your-username/cbbc-app:v1.0.0
```

### Build Options

The Dockerfile supports multi-stage builds for optimized production images:

- **Build stage**: Installs dependencies and builds the application
- **Production stage**: Creates a minimal runtime image with only necessary components

## Pushing to DockerHub

### Setting Up Private Repository

1. **Create a private repository on DockerHub**:
   - Go to https://hub.docker.com/repository/create
   - Set repository name: `cbbc-app`
   - Set visibility to **Private**
   - Click "Create"

2. **Login to DockerHub**:
   ```bash
   docker login
   ```

### Using the Push Script

```bash
# Push with default settings
./scripts/push.sh

# Push with custom parameters
./scripts/push.sh -u your-username -v v1.0.0
```

### Manual Push

```bash
# Push specific version
docker push your-username/cbbc-app:v1.0.0

# Push latest
docker push your-username/cbbc-app:latest
```

## Deployment Options

### Option 1: Docker Compose (Recommended)

This is the recommended deployment method for production:

```bash
# Deploy with default configuration
./scripts/deploy.sh

# Deploy with custom settings
./scripts/deploy.sh -u your-username -v v1.0.0

# Deploy with nginx reverse proxy
docker-compose -f docker-compose.prod.yml --profile with-nginx up -d
```

### Option 2: Standalone Docker

For simple deployments without additional services:

```bash
# Run the application container
docker run -d \
  --name cbbc-app \
  -p 8000:8000 \
  -v cbbc_data:/app/data \
  -v cbbc_instance:/app/instance \
  --env-file .env.prod \
  your-username/cbbc-app:latest

# Run selenium container (required for web scraping)
docker run -d \
  --name cbbc-selenium \
  -p 4444:4444 \
  -p 7900:7900 \
  --shm-size=2g \
  selenium/standalone-chrome:latest
```

### Option 3: Kubernetes

For enterprise deployments, Kubernetes manifests can be created based on the Docker Compose configuration.

## Configuration

### Environment Variables

Key environment variables for production:

```bash
# Application
FLASK_ENV=production
DATABASE_PLATFORM=sqlite  # or postgresql
OUT_PATH=/app/data

# Security (set these in production!)
SECRET_KEY=your-secret-key-here

# Database (if using PostgreSQL)
POSTGRES_USER=cbbc_user
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=cbbc_db
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Performance
GUNICORN_WORKERS=1
GUNICORN_TIMEOUT=120
```

### Database Options

#### SQLite (Default)
- **Pros**: Simple setup, no additional services required
- **Cons**: Limited concurrent access, not suitable for high-traffic deployments
- **Use case**: Single-user deployments, development, testing

#### PostgreSQL
- **Pros**: Better performance, concurrent access, production-ready
- **Cons**: Requires additional database service
- **Use case**: Production deployments, multiple users

To use PostgreSQL:
1. Set `DATABASE_PLATFORM=postgresql`
2. Configure PostgreSQL connection parameters
3. Add PostgreSQL service to docker-compose.yml

### Volume Mounts

Important directories to persist:

- `/app/data`: Application data and logs
- `/app/instance`: SQLite database (if using SQLite)
- `/home/<USER>/Downloads`: Selenium downloads (in selenium container)

## Monitoring and Maintenance

### Health Checks

The application includes built-in health checks:

```bash
# Check application health
curl http://localhost:8000/api/market-status

# Check container health
docker ps  # Look for "healthy" status
```

### Viewing Logs

```bash
# View application logs
./scripts/deploy.sh logs app 100

# View all service logs
docker-compose -f docker-compose.prod.yml logs -f

# View specific service logs
docker-compose -f docker-compose.prod.yml logs -f selenium
```

### Updating the Application

```bash
# Build new version
./scripts/build.sh -v v1.1.0

# Push to DockerHub
./scripts/push.sh -v v1.1.0

# Deploy new version
./scripts/deploy.sh -v v1.1.0
```

### Backup and Restore

```bash
# Backup data
docker run --rm -v cbbc_data:/data -v $(pwd):/backup alpine tar czf /backup/cbbc-data-backup.tar.gz -C /data .

# Restore data
docker run --rm -v cbbc_data:/data -v $(pwd):/backup alpine tar xzf /backup/cbbc-data-backup.tar.gz -C /data
```

## Troubleshooting

### Common Issues

1. **Container fails to start**:
   ```bash
   # Check logs
   docker logs cbbc-app
   
   # Check configuration
   docker exec cbbc-app env | grep -E "(FLASK|DATABASE|OUT_PATH)"
   ```

2. **Database connection issues**:
   ```bash
   # For SQLite, check file permissions
   docker exec cbbc-app ls -la /app/instance/
   
   # For PostgreSQL, check connection
   docker exec cbbc-app python -c "from config import get_config; print(get_config().get_database_url())"
   ```

3. **Selenium connection issues**:
   ```bash
   # Check selenium container status
   docker logs cbbc-selenium

   # Test selenium connection
   curl http://localhost:4444/wd/hub/status

   # Common error: "Connection refused" or "Name or service not known"
   # Solution: Ensure both containers are on the same network
   docker network ls
   docker inspect cbbc_cbbc_network

   # Check if containers can communicate
   docker exec cbbc-app ping cbbc-selenium
   ```

4. **Web scraping/download issues**:
   ```bash
   # Check if files are being downloaded
   docker exec cbbc-app ls -la /app/downloads/
   docker exec cbbc-selenium ls -la /home/<USER>/Downloads/

   # Check volume permissions (common issue)
   docker exec cbbc-app ls -la /app/downloads/
   # Should show: drwxrwxrwx (777 permissions)

   # Fix volume permissions if needed
   docker exec --user root cbbc-app chmod -R 777 /app/downloads

   # Access Selenium VNC for visual debugging
   # Open http://localhost:7900 in browser (no password)
   ```

5. **Volume sharing issues between containers**:
   ```bash
   # Test file sharing between containers
   docker exec cbbc-selenium touch /home/<USER>/Downloads/test.txt
   docker exec cbbc-app ls -la /app/downloads/test.txt

   # If file not visible, check volume mounts
   docker inspect cbbc-app | grep -A 10 "Mounts"
   docker inspect cbbc-selenium | grep -A 10 "Mounts"
   ```

6. **Permission issues**:
   ```bash
   # Check file ownership
   docker exec cbbc-app ls -la /app/

   # Fix permissions if needed
   docker exec --user root cbbc-app chown -R appuser:appuser /app/data

   # Common issue: App container running as wrong user
   docker exec cbbc-app whoami
   # Should show: root (will switch to appuser via entrypoint)

   # Check entrypoint execution
   docker logs cbbc-app | grep "entrypoint"
   ```

### Performance Tuning

1. **Increase worker processes** (if you have multiple CPU cores):
   ```bash
   # Set in environment
   GUNICORN_WORKERS=2
   ```

2. **Adjust memory limits**:
   ```yaml
   # In docker-compose.yml
   services:
     app:
       deploy:
         resources:
           limits:
             memory: 1G
           reservations:
             memory: 512M
   ```

3. **Enable nginx caching** (if using nginx profile):
   - Uncomment caching directives in nginx.conf
   - Add cache volume in docker-compose.yml

### Getting Help

1. **Check application logs** for error messages
2. **Verify configuration** using environment variables
3. **Test individual components** (database, selenium) separately
4. **Review Docker container status** and resource usage

For additional support, check the application logs and container status before reporting issues.

## Quick Reference

### Essential Commands

```bash
# Build and deploy (complete workflow)
export DOCKER_USERNAME=your-dockerhub-username
./scripts/build.sh && ./scripts/push.sh && ./scripts/deploy.sh

# Stop application
./scripts/deploy.sh stop

# View logs
./scripts/deploy.sh logs app 50

# Update to new version
./scripts/build.sh -v v1.1.0 && ./scripts/push.sh -v v1.1.0 && ./scripts/deploy.sh -v v1.1.0

# Backup data
docker run --rm -v cbbc_data:/data -v $(pwd):/backup alpine tar czf /backup/backup-$(date +%Y%m%d).tar.gz -C /data .
```

### File Structure

```
.
├── Dockerfile                 # Production Docker image
├── docker-compose.prod.yml    # Production deployment
├── .dockerignore             # Files to exclude from build
├── .env.example              # Environment template
├── .env.prod                 # Production environment
├── config.py                 # Application configuration
├── gunicorn.conf.py          # Gunicorn settings
├── nginx.conf                # Nginx configuration
├── scripts/
│   ├── build.sh              # Build Docker image
│   ├── push.sh               # Push to DockerHub
│   └── deploy.sh             # Deploy application
└── DEPLOYMENT.md             # This documentation
```

### Port Mapping

- **8000**: Main application (Dash/Flask)
- **4444**: Selenium WebDriver
- **7900**: Selenium VNC (for debugging)
- **80**: Nginx HTTP (if using nginx profile)
- **443**: Nginx HTTPS (if configured)

### Default Credentials

- **Application**: No authentication by default
- **Selenium VNC**: No password (SE_VNC_NO_PASSWORD=1)

### Important URLs

- **Application**: http://localhost:8000
- **Health Check**: http://localhost:8000/api/market-status
- **Selenium Grid**: http://localhost:4444
- **Selenium VNC**: http://localhost:7900 (for debugging)
