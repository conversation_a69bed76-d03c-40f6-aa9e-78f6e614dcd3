# %%
import time
import datetime as dt
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
import pandas as pd
import logging
import sqlalchemy
from sqlalchemy import create_engine
from dotenv import load_dotenv
import os

# Import logging configuration
from logging_config import setup_logging, get_logger, get_hk_time, format_hk_time

# Suppress pandas warnings
pd.options.mode.chained_assignment = None

# Setup logging
setup_logging()

# Get specialized logger for saveCBBC operations
cbbc_logger = get_logger('saveCBBC_worker')

load_dotenv()
platform=os.environ.get('platform')
cbbc_logger.info(f"Platform: {platform}")
chrome_path=os.environ.get('heroku ps')
cbbc_logger.debug(f"Chrome path: {chrome_path}")
chrome_driver_path=os.environ.get('CHROMEDRIVER_PATH')
cbbc_logger.debug(f"Chrome driver path: {chrome_driver_path}")
selenium_hub_url = os.environ.get('SELENIUM_HUB_URL', 'http://localhost:4444/wd/hub')
cbbc_logger.info(f"Selenium Hub URL: {selenium_hub_url}")
if platform == 'local':
    db = os.environ.get('LOCAL_DATABASE_URL')
elif platform == 'heroku':
    db = os.environ.get('HEROKU_DATABASE_URL')
elif platform == 'sqlite':
    db = 'sqlite:///instance/app.db'  # Use an instance directory for the DB file
    # Ensure the instance directory exists
    if not os.path.exists('instance'):
        os.makedirs('instance')
else:
    db = os.environ.get('DATABASE_URL')

cbbc_logger.info(f"Database configured: {bool(db)}")

if db is None:
    cbbc_logger.error("No database URL found. Please check your environment variables.")
    raise ValueError("No database URL found. Please check your environment variables.")

remote_db = create_engine(db)
cbbc_logger.info("Database connection established")

download_path = os.environ.get('download_path') or '/app/download/'
cbbc_logger.debug(f"Download path: {download_path}")

# Get Selenium Hub URL from environment variable
selenium_hub_url = os.environ.get('SELENIUM_HUB_URL', 'http://localhost:4444/wd/hub')
cbbc_logger.debug(f"Selenium Hub URL: {selenium_hub_url}")

def roundUpToMultiple(number, multiple):
    num = number + (multiple - 1)
    return num - (num % multiple)

def roundDnToMultiple(number, multiple):
    return number - (number % multiple)  
#%%
def get_data():
    global download_path
    cbbc_logger.info(f"Starting data download from AASTOCKS at {format_hk_time()}")

    # Clear download_path files
    try:
        files_deleted = 0
        for file_name in os.listdir(download_path):
            file_path = os.path.join(download_path, file_name)
            if os.path.isfile(file_path):
                os.remove(file_path)
                files_deleted += 1
                cbbc_logger.debug(f"Deleted: {file_path}")
        cbbc_logger.info(f"Cleared {files_deleted} files from download directory")
    except OSError as e:
        cbbc_logger.error(f"Error clearing download directory {download_path}: {e}")

    # Read from AASTOCKS
    url = "http://www.aastocks.com/en/stocks/cbbc/search.aspx?t=1&s=&o=1&p=&symbol=110000&search=A|||||||||"
    cbbc_logger.info(f"Fetching data from: {url}")

    # cwd = os.getcwd()
    options = webdriver.ChromeOptions()
    options.add_argument('--ignore-ssl-errors=yes')
    options.add_argument('--ignore-certificate-errors')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument("--allow-running-insecure-content")
    options.add_argument("--disable-features=InsecureDownloadWarnings")
    options.add_argument("--safebrowsing-disable-download-protection")
    options.add_argument("--safebrowsing-disable-extension-blacklist")
    options.add_argument("--unsafely-treat-insecure-origin-as-secure=http://www.aastocks.com/en/stocks/cbbc/")
    # Additional options for Docker environment
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-gpu')
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-background-timer-throttling')
    options.add_argument('--disable-backgrounding-occluded-windows')
    options.add_argument('--disable-renderer-backgrounding')
    # options.add_argument('--headless=old')  # Uncomment for headless mode
    options.add_experimental_option("prefs", {
        "download.default_directory": "/home/<USER>/Downloads",  # Selenium container download path
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": False
    })
    # chrome_options.add_argument('--window-size=1920,1080')

    driver = None
    try:
        cbbc_logger.debug("Initializing Chrome WebDriver")
        cbbc_logger.debug(f"Connecting to Selenium Hub at: {selenium_hub_url}")
        driver = webdriver.Remote(
            command_executor=selenium_hub_url,
            options=options
        )
        # driver.maximize_window()
        cbbc_logger.debug("Navigating to AASTOCKS page")
        driver.get(url)
        cbbc_logger.debug(f"Page loaded, title: {driver.title}")

        wait = WebDriverWait(driver, 10)  # Increased timeout
        cbbc_logger.debug("Looking for Excel download button with class 'icon-excel'")

        try:
            button = wait.until(EC.element_to_be_clickable((By.CLASS_NAME, "icon-excel")))
            cbbc_logger.debug(f"Found Excel button: {button.tag_name}, text: '{button.text}', visible: {button.is_displayed()}")

            # Click on the button
            cbbc_logger.debug("Clicking Excel download button")
            button.click()
            cbbc_logger.info("Download button clicked, waiting 30 seconds for download completion")
            time.sleep(30)  # Increased wait time

            # Check if any files were downloaded
            cbbc_logger.debug("Checking download directory for files")
            files_in_download = os.listdir(download_path)
            cbbc_logger.debug(f"Files in download directory: {files_in_download}")

            cbbc_logger.info("Data download process completed")
        except Exception as download_error:
            cbbc_logger.error(f"Error during download process: {download_error}")
            # Take a screenshot for debugging
            try:
                screenshot_path = os.path.join(download_path, f"error_screenshot_{get_hk_time().strftime('%Y%m%d_%H%M%S')}.png")
                driver.save_screenshot(screenshot_path)
                cbbc_logger.debug(f"Screenshot saved to: {screenshot_path}")
            except Exception as screenshot_error:
                cbbc_logger.warning(f"Could not save screenshot: {screenshot_error}")
            raise
    except Exception as e:
        cbbc_logger.error(f"Error during data download: {e}")
        raise
    finally:
        if driver:
            cbbc_logger.debug("Closing WebDriver")
            driver.close()
            driver.quit()

# %%
def process_data():
    global download_path
    cbbc_logger.info(f"Starting data processing at {format_hk_time()}")

    # Import Datafile
    try:
        files = [f for f in os.listdir(download_path)
                 if os.path.isfile(os.path.join(download_path, f)) and f.lower().endswith('.xlsx')]
        if not files:
            cbbc_logger.error("No Excel files found in download directory")
            raise FileNotFoundError("No Excel file found in download directory")
        # Pick the newest file by modification time
        datafile = max(files, key=lambda f: os.path.getmtime(os.path.join(download_path, f)))
        cbbc_logger.debug(f"Processing file: {datafile}")
    except Exception as e:
        cbbc_logger.error(f"Error accessing download directory: {e}")
        raise

    # datafile=max((f for f in os.listdir(download_path) if f.endswith('.xlsx')), key=lambda f: os.path.getmtime(os.path.join(download_path, f)))
    # time_stamp = "20240106_0000" #datafile[16:-5]
    # Get time now in HKG timezone
    time_stamp = get_hk_time().strftime("%Y%m%d_%H%M")
    cbbc_logger.info(f"Processing timestamp: {time_stamp}")

    df = pd.read_excel(download_path+datafile)
    cbbc_logger.info(f"READ {download_path+datafile}: {len(df)} rows")

    # Filter df with underlying=='HSI'
    original_rows = len(df)
    df = df[df['Underlying'] == 'HSI'].copy()    # Cleasing
    hsi_rows = len(df)
    cbbc_logger.info(f"Filtered to HSI data: {hsi_rows} rows (from {original_rows} total)")

    df = df.dropna(subset=['Last']).copy()
    clean_rows = len(df)
    cbbc_logger.debug(f"After removing null 'Last' values: {clean_rows} rows")

    if df['O/S'].dtypes =='object':
        df.loc[:, 'OS_QTY2'] = df['O/S'].str.replace('K','E+3').str.replace('M','E+6')
        df.loc[:, 'OS_QTY2'] = pd.to_numeric(df['OS_QTY2'], downcast='integer')
    else:
        df.loc[:, 'OS_QTY2'] = df['O/S']
    df.loc[:, 'OS_VAL'] = round(df['OS_QTY2']*df['Last'],0)

    if df['Turn.'].dtypes =='object':
        df.loc[:, 'TURNOVER'] = df['Turn.'].str.replace('K','E+3').str.replace('M','E+6').str.replace('B','E+9')
        df.loc[:, 'TURNOVER'] = pd.to_numeric(df['TURNOVER'], downcast='integer')
    else:
        df.loc[:, 'TURNOVER'] = df['Turn.']
    # df['TURN_AMT']=round(df['TURNOVER']*df['Last#'], -3)
    df.loc[:, 'TURN_AMT'] = round(df['TURNOVER'])    # Drop rows with 0 OS_VAL and TURN_AMT
    df = df[(df['OS_VAL'] != 0) | (df['TURN_AMT'] != 0)].copy()
    final_rows = len(df)
    cbbc_logger.info(f"After filtering zero values: {final_rows} rows")

    df.loc[:, 'CELL_RANGE'] = df['Call Lv'].apply(lambda s: roundDnToMultiple(s, 100))
    cbbc_logger.debug("Data transformation completed")

    # SORT BY CELL_RANGE/OS_AMT
    df_cum_bull = df[df['Bull/Bear']=='Bull'].groupby(['Call Lv']).agg({'OS_VAL':'sum', 'TURN_AMT':'sum'})
    df_cum_bull = df_cum_bull.sort_values(by='Call Lv', ascending=False).reset_index()
    df_cum_bull['CUM_OS_VAL']=df_cum_bull['OS_VAL'].cumsum()
    df_cum_bull['Bull/Bear']='Bull'

    df_cum_bear = df[df['Bull/Bear']=='Bear'].groupby(['Call Lv']).agg({'OS_VAL':'sum', 'TURN_AMT':'sum'}).reset_index()
    # df_cum_bear = df_cum_bear.sort_values(by='Call Lv', ascending=False)
    df_cum_bear['CUM_OS_VAL']=df_cum_bear['OS_VAL'].cumsum()
    df_cum_bear['Bull/Bear']='Bear'

    df_cum = pd.concat([df_cum_bull, df_cum_bear])
    df_cum['txn_date']= time_stamp
    # Save to database
    df_cum.rename(columns = {'Bull/Bear':'BULLBEAR', 'Call Lv':'CallLv'}, inplace = True) 
    tname='hsi_cbbc_cum'
    df_cum.to_sql(name= tname, con=remote_db, if_exists = 'replace', index=False,
        dtype={'BULLBEAR': sqlalchemy.types.VARCHAR(length=20)}
        )    

    # Query the database to confirm data insertion
    try:
        with remote_db.connect() as con:
            query = f"SELECT * FROM {tname} WHERE txn_date = '{time_stamp}' LIMIT 5;"
            result = con.execute(query).fetchall()
            cbbc_logger.info(f"Database query successful: {result}")
    except Exception as e:
        cbbc_logger.error(f"Database query failed: {e}")
        raise

    # Create Summary df
    # df_near = df[(df['CELL_RANGE']>= 18000) & (df['CELL_RANGE']<= 26000) ]
    # df_near = df[(df['CELL_RANGE']>= 18000) & (df['CELL_RANGE']<= 22000) & ((df['OS_VAL']> 0 + df['TURNOVER'])>0)]
    df_sum = df.groupby(['CELL_RANGE','Bull/Bear']).agg({'OS_VAL':'sum', 'TURN_AMT':'sum'}).reset_index()
    # df_sum = df_sum[ (df_sum['OS_VAL']>0) + (df_sum['TURN_AMT']>0)]
    df_sum['txn_date']= time_stamp

    # Save to database
    df_sum.rename(columns = {'Bull/Bear':'BULLBEAR'}, inplace = True) 
    tname='hsi_cbbc_sum'
    df_sum.to_sql(name= tname, con=remote_db, if_exists = 'replace', index=False,
        dtype={'BULLBEAR': sqlalchemy.types.VARCHAR(length=20)}
        )        # Archive to History table
    # sql_delete=f"DELETE FROM public.hsi_cbbc_sum_hist where trade_date = '{time_stamp[0:8]}';"
    # sql_insert=f"INSERT INTO public.hsi_cbbc_sum_hist SELECT '{time_stamp[0:8]}',* FROM public.hsi_cbbc_sum;"
    # with remote_db.connect() as con:
    #     rs = con.execute(sql_delete)
    #     rs = con.execute(sql_insert)
    # Save Result Summary
    if not os.path.exists(download_path+'/xls/'):
        os.mkdir(download_path+'/xls/')
        cbbc_logger.info(f"Directory Created: {download_path}/xls/")
    pathname=str(f'{download_path}/xls/{time_stamp[0:8]}/')
    if not os.path.exists(pathname):
        os.mkdir(pathname)
        cbbc_logger.info(f"Directory Created: {pathname}")

    # Save Excel files
    try:
        df.to_excel(pathname + f'cbbc_{time_stamp}_raw.xlsx', encoding='UTF8', index = False)
        df_sum.to_excel(pathname + f'cbbc_{time_stamp}_sum.xlsx', encoding='UTF8', index = False)
        df_cum.sort_values(by='CallLv', ascending=True).to_excel(pathname + f'cbbc_{time_stamp}_cum.xlsx', encoding='UTF8', index = False)
        cbbc_logger.info(f"Excel files saved successfully to {pathname}")
    except Exception as e:
        cbbc_logger.error(f"Failed to save Excel files: {e}")
        raise

    # Query the database to confirm data insertion
    try:
        with remote_db.connect() as con:
            query = f"SELECT * FROM {tname} WHERE txn_date = '{time_stamp}' LIMIT 5;"
            result = con.execute(query).fetchall()
            cbbc_logger.info(f"Database query successful: {result}")
    except Exception as e:
        cbbc_logger.error(f"Database query failed: {e}")
        raise
    cbbc_logger.info(f"Data processing completed successfully at {format_hk_time()}")
    # return time_stamp


# %%
def saveCBBC_XLS():
    """
    Main function to download and process CBBC data from AASTOCKS.
    Logs all operations with Hong Kong timezone.
    """
    start_time = get_hk_time()
    cbbc_logger.info(f"=== CBBC Data Update Started at {format_hk_time(start_time)} ===")

    try:
        get_data()
        process_data()

        end_time = get_hk_time()
        duration = (end_time - start_time).total_seconds()
        cbbc_logger.info(f"=== CBBC Data Update Completed at {format_hk_time(end_time)} (Duration: {duration:.2f}s) ===")

    except Exception as e:
        end_time = get_hk_time()
        duration = (end_time - start_time).total_seconds()
        cbbc_logger.error(f"=== CBBC Data Update Failed at {format_hk_time(end_time)} (Duration: {duration:.2f}s) ===")
        cbbc_logger.error(f"Error details: {e}")
        raise
#%%
if __name__ == '__main__':
    cbbc_logger.info("Running saveCBBC_XLS as standalone script")
    saveCBBC_XLS()
# %%
# process_data()
# %%
