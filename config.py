"""
Production configuration for CBBC Application
"""

import os
from typing import Optional


class Config:
    """Base configuration class"""
    
    # Application settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    FLASK_ENV = os.environ.get('FLASK_ENV', 'production')
    DEBUG = False
    TESTING = False
    
    # Database settings
    DATABASE_PLATFORM = os.environ.get('DATABASE_PLATFORM', 'sqlite')
    DATABASE_URL = os.environ.get('DATABASE_URL')
    
    # PostgreSQL settings (if using postgresql)
    POSTGRES_USER = os.environ.get('POSTGRES_USER', 'cbbc_user')
    POSTGRES_PASSWORD = os.environ.get('POSTGRES_PASSWORD')
    POSTGRES_DB = os.environ.get('POSTGRES_DB', 'cbbc_db')
    POSTGRES_HOST = os.environ.get('POSTGRES_HOST', 'localhost')
    POSTGRES_PORT = os.environ.get('POSTGRES_PORT', '5432')
    
    # Paths
    OUT_PATH = os.environ.get('OUT_PATH', '/app/data')
    LOGS_PATH = os.path.join(OUT_PATH, 'logs')
    DOWNLOAD_PATH = os.path.join(OUT_PATH, 'download')
    
    # Selenium settings
    SELENIUM_HUB_URL = os.environ.get('SELENIUM_HUB_URL', 'http://selenium:4444/wd/hub')
    
    # Logging settings
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FORMAT = os.environ.get('LOG_FORMAT', 'detailed')
    
    # Auto-update settings
    AUTO_UPDATE_ENABLED = os.environ.get('AUTO_UPDATE_ENABLED', 'true').lower() == 'true'
    UPDATE_INTERVAL_MINUTES = int(os.environ.get('UPDATE_INTERVAL_MINUTES', '15'))
    
    # Market hours (Hong Kong time)
    MARKET_OPEN_HOUR = int(os.environ.get('MARKET_OPEN_HOUR', '9'))
    MARKET_OPEN_MINUTE = int(os.environ.get('MARKET_OPEN_MINUTE', '30'))
    MARKET_CLOSE_HOUR = int(os.environ.get('MARKET_CLOSE_HOUR', '16'))
    MARKET_CLOSE_MINUTE = int(os.environ.get('MARKET_CLOSE_MINUTE', '0'))
    
    # Performance settings
    GUNICORN_WORKERS = int(os.environ.get('GUNICORN_WORKERS', '1'))
    GUNICORN_TIMEOUT = int(os.environ.get('GUNICORN_TIMEOUT', '120'))
    GUNICORN_MAX_REQUESTS = int(os.environ.get('GUNICORN_MAX_REQUESTS', '1000'))
    
    # Security settings
    SECURE_SSL_REDIRECT = os.environ.get('SECURE_SSL_REDIRECT', 'false').lower() == 'true'
    SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'false').lower() == 'true'
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    @classmethod
    def get_database_url(cls) -> str:
        """Get the appropriate database URL based on platform"""
        if cls.DATABASE_PLATFORM == 'postgresql':
            if cls.DATABASE_URL:
                return cls.DATABASE_URL
            else:
                return f"postgresql://{cls.POSTGRES_USER}:{cls.POSTGRES_PASSWORD}@{cls.POSTGRES_HOST}:{cls.POSTGRES_PORT}/{cls.POSTGRES_DB}"
        else:
            # Default to SQLite
            return 'sqlite:///instance/app.db'
    
    @classmethod
    def init_directories(cls):
        """Initialize required directories"""
        directories = [
            cls.OUT_PATH,
            cls.LOGS_PATH,
            cls.DOWNLOAD_PATH,
            os.path.join(cls.DOWNLOAD_PATH, 'xls'),
            'instance'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)


class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Enhanced security for production
    SESSION_COOKIE_SECURE = True
    SECURE_SSL_REDIRECT = os.environ.get('SECURE_SSL_REDIRECT', 'false').lower() == 'true'
    
    # Production logging
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')


class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    
    # Development logging
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'DEBUG')


class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    
    # Use in-memory SQLite for testing
    DATABASE_PLATFORM = 'sqlite'
    
    # Testing paths
    OUT_PATH = '/tmp/cbbc_test'
    
    # Disable auto-update for testing
    AUTO_UPDATE_ENABLED = False


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': ProductionConfig
}


def get_config() -> Config:
    """Get configuration based on environment"""
    env = os.environ.get('FLASK_ENV', 'production')
    return config.get(env, config['default'])
